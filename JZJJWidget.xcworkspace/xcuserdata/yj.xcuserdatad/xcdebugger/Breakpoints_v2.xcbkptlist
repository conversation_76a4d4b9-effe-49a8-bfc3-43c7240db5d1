<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "2F4587C5-16C8-4C54-AE70-CF8351F7F86F"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2939AFD5-7C99-41DC-8751-855FA8A8B1CD"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "KeyBoardExtension/KeyboardImageLoader.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "161"
            endingLineNumber = "161"
            landmarkName = "loadIndividualKeyImage(for:keyConfig:themeId:state:isBuiltIn:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "64FBFD5B-1585-4DFF-B874-C8C0A453C8A4"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackImageManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "246"
            endingLineNumber = "246"
            landmarkName = "loadImageFromFile(at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "A667DB26-DA1F-4BAD-9996-B7EBD9D167A9"
            shouldBeEnabled = "No"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "230"
            endingLineNumber = "230"
            landmarkName = "applyThemePack(id:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "BC527E80-610E-486B-A775-0CB235D8701D"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "238"
            endingLineNumber = "238"
            landmarkName = "applyThemePack(id:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "EA49DD0F-D05A-40C7-AD83-581AF55509A3"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "2510"
            endingLineNumber = "2510"
            landmarkName = "convertToAdvancedTheme(from:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "75F2A194-690D-449C-93DB-7DCAF1963A9C"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "196"
            endingLineNumber = "196"
            landmarkName = "getThemePack(id:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
